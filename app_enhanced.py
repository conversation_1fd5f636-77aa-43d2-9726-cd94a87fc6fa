# 🧠 Enhanced AWS Cost Optimization Assistant with Session Discovery
# AI-powered AWS analysis with conversation context, memory, and session discovery

"""
Enhanced Streamlit Frontend with Context Retention and Session Discovery
Provides a rich UI for the context-aware MCP bot with session management
"""

import streamlit as st
import requests
import json
import uuid
from datetime import datetime
import time
import pandas as pd
from typing import Optional, Dict, List, Any
import plotly.express as px
import plotly.graph_objects as go
from urllib.parse import urljoin
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")
API_TIMEOUT = int(os.getenv("API_TIMEOUT", "120"))

# Page configuration
st.set_page_config(
    page_title="Enhanced AWS Cost Optimization Assistant",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Enhanced CSS for context-aware features
st.markdown("""
<style>
.context-indicator {
    background: linear-gradient(45deg, #1f4e79, #2e6da4);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    margin: 5px 0;
    display: inline-block;
}

.session-selector {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 10px;
    margin: 10px 0;
}

.discovery-result {
    background: #e8f4fd;
    border-left: 4px solid #0066cc;
    padding: 10px;
    margin: 5px 0;
    border-radius: 4px;
}

.metric-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin: 10px 0;
}

.session-preview {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 12px;
    margin: 8px 0;
    background: #fafafa;
}

.session-active {
    border-left: 4px solid #28a745;
    background: #f8fff9;
}

.session-restored {
    border-left: 4px solid #17a2b8;
    background: #f0f9ff;
}
</style>
""", unsafe_allow_html=True)

# Enhanced API Client Class
class EnhancedAPIClient:
    def __init__(self, base_url: str, timeout: int = 120):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout

    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """Make HTTP request with error handling"""
        url = urljoin(self.base_url + '/', endpoint.lstrip('/'))
        try:
            response = requests.request(method, url, timeout=self.timeout, **kwargs)
            response.raise_for_status()
            return response
        except requests.exceptions.Timeout:
            st.error(f"Request timed out after {self.timeout} seconds")
            raise
        except requests.exceptions.ConnectionError:
            st.error(f"Unable to connect to the API server at {self.base_url}")
            raise
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 503:
                st.error("Service is initializing. Please wait a moment and try again.")
            else:
                st.error(f"API Error ({e.response.status_code}): {e.response.text}")
            raise
        except Exception as e:
            st.error(f"Unexpected error: {str(e)}")
            raise

    def health_check(self) -> Dict:
        """Check API health status with session stats"""
        response = self._make_request("GET", "/")
        return response.json()

    def chat_with_context(self, message: str, session_id: str) -> Dict:
        """Send chat message with context retention"""
        payload = {
            "message": message,
            "conversation_id": session_id,
            "use_tools": True
        }
        response = self._make_request("POST", "/chat", json=payload)
        return response.json()

    def discover_sessions(self, search_term: str = "mcp-bot") -> Dict:
        """Discover available sessions from Bedrock"""
        response = self._make_request("GET", f"/sessions/discover?search_term={search_term}")
        return response.json()

    def discover_all_sessions(self) -> Dict:
        """Discover all sessions with detailed metadata"""
        response = self._make_request("GET", "/sessions/discover/all")
        return response.json()

    def restore_session(self, session_id: str) -> Dict:
        """Restore a session from Bedrock"""
        response = self._make_request("POST", f"/sessions/restore/{session_id}")
        return response.json()

    def restore_and_continue_session(self, session_id: str) -> Dict:
        """Restore session and prepare for continuation"""
        response = self._make_request("POST", f"/sessions/restore/{session_id}/continue")
        return response.json()

    def get_session_preview(self, session_id: str) -> Dict:
        """Get session preview without full restoration"""
        response = self._make_request("GET", f"/sessions/{session_id}/preview")
        return response.json()

    def list_bedrock_sessions(self) -> Dict:
        """List all Bedrock sessions"""
        response = self._make_request("GET", "/sessions/bedrock/list")
        return response.json()

    def get_session_history(self, session_id: str) -> Dict:
        """Get conversation history for a session"""
        response = self._make_request("GET", f"/sessions/{session_id}/history")
        return response.json()

    def get_session_stats(self, session_id: str) -> Dict:
        """Get detailed session statistics"""
        response = self._make_request("GET", f"/sessions/{session_id}/stats")
        return response.json()

    def clear_session(self, session_id: str) -> Dict:
        """Clear a session's conversation history"""
        response = self._make_request("DELETE", f"/sessions/{session_id}")
        return response.json()

    def list_sessions(self) -> Dict:
        """List all active sessions"""
        response = self._make_request("GET", "/sessions")
        return response.json()

    def list_tools(self) -> Dict:
        """List available tools"""
        response = self._make_request("GET", "/tools")
        return response.json()

# Initialize Enhanced API client
@st.cache_resource
def get_enhanced_api_client():
    return EnhancedAPIClient(API_BASE_URL, API_TIMEOUT)

# Enhanced session state initialization
def initialize_enhanced_session_state():
    if 'session_id' not in st.session_state:
        st.session_state.session_id = f"streamlit_{uuid.uuid4().hex[:12]}"
    if 'conversation_history' not in st.session_state:
        st.session_state.conversation_history = []
    if 'api_status' not in st.session_state:
        st.session_state.api_status = None
    if 'session_stats' not in st.session_state:
        st.session_state.session_stats = None
    if 'context_enabled' not in st.session_state:
        st.session_state.context_enabled = True
    if 'auto_load_history' not in st.session_state:
        st.session_state.auto_load_history = True
    if 'session_restored' not in st.session_state:
        st.session_state.session_restored = False

def display_context_indicator(context_used: bool, session_stats: Dict = None):
    """Display context retention indicator"""
    if context_used:
        st.markdown("""
        <div class="context-indicator">
            🧠 Context Active - AI remembers previous conversation
        </div>
        """, unsafe_allow_html=True)
        
        if session_stats:
            turns = session_stats.get('total_turns', 0)
            tools = session_stats.get('total_tools_used', 0)
            st.caption(f"Session: {turns} turns, {tools} tools used")

def load_session_discovery_sidebar():
    """Enhanced session discovery using pure Bedrock APIs"""
    api_client = get_enhanced_api_client()
    
    st.sidebar.header("🔍 Session Recovery & Discovery")
    
    try:
        # Session discovery interface
        with st.sidebar.expander("🔍 Discover Sessions", expanded=True):
            col1, col2 = st.columns(2)
            
            with col1:
                if st.button("🔄 Refresh", key="refresh_sessions"):
                    st.cache_data.clear()
                    st.rerun()
            
            with col2:
                search_term = st.text_input("Search term:", value="mcp-bot", key="search_term", label_visibility="collapsed")
        
        # Cached session discovery
        @st.cache_data(ttl=30)
        def discover_sessions_cached():
            return api_client.discover_all_sessions()
        
        discovered = discover_sessions_cached()
        available_sessions = discovered.get("sessions", [])
        
        if available_sessions:
            st.sidebar.success(f"Found {len(available_sessions)} recoverable sessions")
            
            # Session display and management
            for i, session in enumerate(available_sessions[:10]):  # Show top 10
                session_id = session["session_id"]
                turn_count = session.get("turn_count", 0)
                last_message = session.get("last_message", "No messages")[:50] + "..."
                last_updated = session.get("last_updated", "Unknown")[:19] if session.get("last_updated") else "Unknown"
                tools_count = session.get("tools_used_count", 0)
                
                # Status indicators
                status_icon = "🟢" if session.get("status") == "ACTIVE" else "🟡"
                memory_icon = "💾" if session.get("in_memory") else "☁️"
                
                # Create expandable session preview
                with st.sidebar.expander(f"{status_icon}{memory_icon} {session_id[:12]}...", expanded=False):
                    st.markdown(f"""
                    <div class="session-preview">
                        <strong>Session Details:</strong><br>
                        💬 <strong>Turns:</strong> {turn_count}<br>
                        🔧 <strong>Tools:</strong> {tools_count}<br>
                        🕒 <strong>Updated:</strong> {last_updated}<br>
                        📝 <strong>Last Message:</strong> {last_message}
                    </div>
                    """, unsafe_allow_html=True)
                    
                    # Action buttons
                    col1, col2, col3 = st.columns(3)
                    
                    with col1:
                        if st.button("👁️", key=f"preview_{i}", help="Quick Preview"):
                            try:
                                preview = api_client.get_session_preview(session_id)
                                st.json(preview["preview"])
                            except Exception as e:
                                st.error(f"Preview failed: {e}")
                    
                    with col2:
                        if st.button("🔄", key=f"restore_{i}", help="Restore & Continue"):
                            try:
                                with st.spinner("Restoring session..."):
                                    # Restore session and prepare for continuation
                                    restore_response = api_client.restore_and_continue_session(session_id)
                                    
                                    # Load conversation history
                                    history_response = api_client.get_session_history(session_id)
                                    
                                    # Update Streamlit session state
                                    st.session_state.session_id = session_id
                                    st.session_state.conversation_history = []
                                    st.session_state.session_restored = True
                                    
                                    # Convert to display format
                                    for turn in history_response.get('history', []):
                                        st.session_state.conversation_history.append({
                                            "user": turn["user_message"],
                                            "assistant": turn["assistant_response"],
                                            "timestamp": turn["timestamp"],
                                            "tools_used": turn.get("tools_used", [])
                                        })
                                    
                                    st.success(f"✅ Restored session with {restore_response['conversation_turns']} turns!")
                                    st.info(f"💡 Context: {restore_response.get('context_summary', 'Available')}")
                                    
                                    # Clear cache and rerun
                                    st.cache_data.clear()
                                    st.rerun()
                                    
                            except Exception as e:
                                st.error(f"❌ Restoration failed: {e}")
                    
                    with col3:
                        if st.button("🗑️", key=f"delete_{i}", help="Delete Session"):
                            try:
                                api_client.clear_session(session_id)
                                st.success(f"Deleted session {session_id[:8]}...")
                                st.cache_data.clear()
                                st.rerun()
                            except Exception as e:
                                st.error(f"Failed to delete: {e}")
        else:
            st.sidebar.info("No recoverable sessions found")
            
        # Advanced discovery options
        with st.sidebar.expander("🔧 Advanced Options"):
            if st.button("📋 List All Bedrock Sessions", key="list_all_bedrock"):
                try:
                    with st.spinner("Fetching all Bedrock sessions..."):
                        bedrock_sessions = api_client.list_bedrock_sessions()
                    
                    st.write("**All Bedrock Sessions:**")
                    sessions = bedrock_sessions.get("bedrock_sessions", [])
                    
                    if sessions:
                        df = pd.DataFrame(sessions)
                        st.dataframe(df, use_container_width=True)
                    else:
                        st.info("No Bedrock sessions found")
                        
                except Exception as e:
                    st.error(f"Failed to list Bedrock sessions: {e}")
            
            # Current session info
            if st.session_state.session_id:
                st.write("**Current Session:**")
                st.code(st.session_state.session_id, language="text")
                
                if st.session_state.session_restored:
                    st.markdown("🔄 **Restored Session** - Full context available")
                
                col1, col2 = st.columns(2)
                with col1:
                    if st.button("📊 Stats", key="show_session_stats"):
                        try:
                            stats = api_client.get_session_stats(st.session_state.session_id)
                            st.json(stats)
                        except Exception as e:
                            st.error(f"Failed to get stats: {e}")
                
                with col2:
                    if st.button("📜 History", key="show_session_history"):
                        try:
                            history = api_client.get_session_history(st.session_state.session_id)
                            st.json(history)
                        except Exception as e:
                            st.error(f"Failed to get history: {e}")
                        
    except Exception as e:
        st.sidebar.warning(f"Session discovery unavailable: {e}")

def display_api_status():
    """Display API connection status"""
    api_client = get_enhanced_api_client()
    
    try:
        if st.session_state.api_status is None:
            with st.spinner("Checking API status..."):
                status = api_client.health_check()
            st.session_state.api_status = status
        
        status = st.session_state.api_status
        
        # Display status in sidebar
        st.sidebar.header("🔧 System Status")
        
        if status.get("status") == "healthy":
            st.sidebar.success("✅ API Connected")
            
            # Display session stats
            session_stats = status.get("session_stats", {})
            if session_stats:
                col1, col2 = st.sidebar.columns(2)
                col1.metric("Active Sessions", session_stats.get("active_sessions", 0))
                col2.metric("Total Sessions", session_stats.get("total_sessions", 0))
            
            # Display features
            features = status.get("features", [])
            if features:
                st.sidebar.write("**Enhanced Features:**")
                for feature in features:
                    st.sidebar.write(f"• {feature.replace('_', ' ').title()}")
        else:
            st.sidebar.error("❌ API Disconnected")
            
    except Exception as e:
        st.sidebar.error(f"❌ API Error: {str(e)}")
        st.session_state.api_status = None

def display_conversation_history():
    """Display conversation history with enhanced formatting"""
    if st.session_state.conversation_history:
        st.header("📜 Conversation History")
        
        if st.session_state.session_restored:
            st.info("🔄 This conversation was restored from a previous session with full context.")
        
        for i, exchange in enumerate(reversed(st.session_state.conversation_history)):
            with st.container():
                col1, col2 = st.columns([1, 20])
                
                with col1:
                    st.write(f"**{len(st.session_state.conversation_history) - i}**")
                
                with col2:
                    # User message
                    with st.chat_message("user"):
                        st.write(exchange["user"])
                    
                    # Assistant response
                    with st.chat_message("assistant"):
                        st.write(exchange["assistant"])
                        
                        # Show tools used if any
                        tools_used = exchange.get("tools_used", [])
                        if tools_used:
                            successful_tools = [t for t in tools_used if t.get("success")]
                            if successful_tools:
                                st.caption(f"🔧 Used {len(successful_tools)} tools")
                
                st.divider()

def display_session_management():
    """Display session management controls"""
    api_client = get_enhanced_api_client()
    
    st.sidebar.header("💾 Session Management")
    
    # Current session display
    st.sidebar.write("**Current Session:**")
    session_display = st.session_state.session_id[:20] + "..." if len(st.session_state.session_id) > 20 else st.session_state.session_id
    st.sidebar.code(session_display, language="text")
    
    if st.session_state.session_restored:
        st.sidebar.markdown("🔄 **Restored Session** - Context Available")
    
    # Session controls
    col1, col2 = st.sidebar.columns(2)
    
    with col1:
        if st.button("🆕 New", key="new_session"):
            st.session_state.session_id = f"streamlit_{uuid.uuid4().hex[:12]}"
            st.session_state.conversation_history = []
            st.session_state.session_stats = None
            st.session_state.session_restored = False
            st.success("Started new session!")
            st.rerun()
    
    with col2:
        if st.button("🗑️ Clear", key="clear_session"):
            if st.session_state.conversation_history:
                try:
                    api_client.clear_session(st.session_state.session_id)
                    st.session_state.conversation_history = []
                    st.session_state.session_stats = None
                    st.session_state.session_restored = False
                    st.success("Session cleared!")
                    st.rerun()
                except Exception as e:
                    st.error(f"Failed to clear session: {e}")
    
    # Context settings
    st.sidebar.write("**Context Settings:**")
    context_enabled = st.sidebar.checkbox(
        "Enable context retention",
        value=st.session_state.context_enabled,
        key="context_toggle"
    )
    st.session_state.context_enabled = context_enabled
    
    if not context_enabled:
        st.sidebar.warning("⚠️ Context disabled - AI won't remember previous messages")

def display_session_continuation_info():
    """Display information about session continuation."""
    if st.session_state.get('session_stats'):
        stats = st.session_state.session_stats
        
        if stats.get('total_turns', 0) > 0:
            st.info(f"""
            🔄 **Session Continued**
            - Session ID: `{st.session_state.session_id}`
            - Conversation turns: {stats.get('total_turns', 0)}
            - Tools used: {stats.get('total_tools_used', 0)}
            - Context preserved: ✅
            """)

def main():
    """Main application function"""
    # Initialize session state
    initialize_enhanced_session_state()
    
    # Title and description
    st.title("🧠 Enhanced AWS Cost Optimization Assistant")
    st.markdown("*AI-powered AWS analysis with conversation context, memory, and session discovery*")
    
    # Load session discovery in sidebar
    load_session_discovery_sidebar()
    
    # Display API status
    display_api_status()
    
    # Display session management
    display_session_management()
    
    # Main chat interface
    api_client = get_enhanced_api_client()
    
    # Display session continuation info if applicable
    display_session_continuation_info()
    
    # Chat input
    user_input = st.chat_input("Ask about AWS costs, pricing, or infrastructure...")
    
    if user_input:
        # Add user message to history immediately
        timestamp = datetime.now().isoformat()
        
        # Display user message
        with st.chat_message("user"):
            st.write(user_input)
        
        # Get AI response
        with st.chat_message("assistant"):
            try:
                with st.spinner("Thinking..."):
                    response = api_client.chat_with_context(user_input, st.session_state.session_id)
                
                # Display response
                st.write(response["response"])
                
                # Display context indicator
                context_used = response.get("context_used", False)
                session_stats = response.get("session_stats")
                display_context_indicator(context_used, session_stats)
                
                # Store in conversation history
                st.session_state.conversation_history.append({
                    "user": user_input,
                    "assistant": response["response"],
                    "timestamp": timestamp,
                    "tools_used": response.get("tools_used", [])
                })
                
                # Update session stats
                if session_stats:
                    st.session_state.session_stats = session_stats
                
                # Auto-scroll by rerunning
                st.rerun()
                
            except Exception as e:
                st.error(f"Error: {str(e)}")
                
                # Add error to history
                st.session_state.conversation_history.append({
                    "user": user_input,
                    "assistant": f"Error: {str(e)}",
                    "timestamp": timestamp,
                    "tools_used": []
                })
    
    # Display conversation history
    if st.session_state.conversation_history:
        display_conversation_history()
    
    # Footer
    st.markdown("---")
    st.markdown("**Enhanced Features:** Context Retention • Session Management • Tool Integration • Session Discovery & Recovery • Bedrock Persistence • Seamless Continuation")

if __name__ == "__main__":
    main()
