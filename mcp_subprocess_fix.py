"""
Windows Store Python subprocess fix for MCP servers
This module provides a workaround for the NotImplementedError in Windows Store Python
"""

import asyncio
import subprocess
import sys
import os
from typing import Dict, List, Optional, Tuple
import logging

logger = logging.getLogger(__name__)

class WindowsSubprocessFix:
    """
    Workaround for Windows Store Python subprocess limitations
    """
    
    @staticmethod
    async def create_subprocess_with_fix(
        command: str,
        args: List[str],
        env: Optional[Dict[str, str]] = None,
        cwd: Optional[str] = None
    ) -> Tuple[asyncio.StreamReader, asyncio.StreamWriter]:
        """
        Create a subprocess with Windows Store Python compatibility
        Returns stdin/stdout streams for MCP communication
        """
        
        # Prepare the full command
        full_command = [command] + args
        
        # Prepare environment
        full_env = os.environ.copy()
        if env:
            full_env.update(env)
        
        logger.info(f"Creating subprocess: {' '.join(full_command)}")
        
        try:
            # Try the standard approach first
            process = await asyncio.create_subprocess_exec(
                *full_command,
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                env=full_env,
                cwd=cwd
            )
            
            return process.stdout, process.stdin
            
        except NotImplementedError:
            logger.warning("Standard subprocess creation failed, trying workaround...")
            
            # Fallback: Use threading with regular subprocess
            import threading
            import queue
            from concurrent.futures import ThreadPoolExecutor
            
            # Create a thread-based subprocess wrapper
            return await WindowsSubprocessFix._create_threaded_subprocess(
                full_command, full_env, cwd
            )
    
    @staticmethod
    async def _create_threaded_subprocess(
        command: List[str],
        env: Dict[str, str],
        cwd: Optional[str]
    ) -> Tuple[asyncio.StreamReader, asyncio.StreamWriter]:
        """
        Create subprocess using threading as a workaround
        """
        
        # This is a complex workaround that would require significant implementation
        # For now, we'll raise a more helpful error
        raise RuntimeError(
            "Windows Store Python detected with subprocess limitations. "
            "Please install regular Python from python.org or use an alternative approach."
        )

def check_python_installation():
    """
    Check if we're running Windows Store Python and warn the user
    """
    python_path = sys.executable
    
    if "WindowsApps" in python_path and "PythonSoftwareFoundation" in python_path:
        return {
            "is_windows_store": True,
            "path": python_path,
            "recommendation": "Install regular Python from python.org for full subprocess support"
        }
    
    return {
        "is_windows_store": False,
        "path": python_path,
        "recommendation": None
    }

def get_alternative_mcp_config():
    """
    Get alternative MCP server configurations that might work better
    """
    
    # Try using direct executable paths if they exist
    import os
    
    home_dir = os.path.expanduser("~")
    uv_tools_dir = os.path.join(home_dir, "AppData", "Roaming", "uv", "tools")
    
    configs = []
    
    # Check for installed tools
    servers = [
        ("cloudformation", "awslabs-cfn-mcp-server", "awslabs.cfn-mcp-server.exe"),
        ("cost-explorer", "awslabs-cost-explorer-mcp-server", "awslabs.cost-explorer-mcp-server.exe"),
        ("aws-pricing", "awslabs-aws-pricing-mcp-server", "awslabs.aws-pricing-mcp-server.exe")
    ]
    
    for name, package, executable in servers:
        tool_dir = os.path.join(uv_tools_dir, package)
        scripts_dir = os.path.join(tool_dir, "Scripts")
        exe_path = os.path.join(scripts_dir, executable)
        
        if os.path.exists(exe_path):
            configs.append({
                "name": name,
                "command": exe_path,
                "args": [],
                "env": {
                    "AWS_PROFILE": os.getenv("AWS_PROFILE", "default"),
                    "AWS_REGION": os.getenv("AWS_REGION", "ap-south-1")
                },
                "description": f"AWS {name.title()} MCP Server (direct path)",
                "enabled": True
            })
        else:
            logger.warning(f"Direct executable not found: {exe_path}")
    
    return configs

if __name__ == "__main__":
    # Test the Python installation
    info = check_python_installation()
    print(f"Python installation info: {info}")
    
    if info["is_windows_store"]:
        print("\n⚠️  WARNING: Windows Store Python detected!")
        print("This version has limitations with subprocess creation.")
        print("Recommendation: Install regular Python from python.org")
        
        # Show alternative configs
        alt_configs = get_alternative_mcp_config()
        if alt_configs:
            print(f"\nFound {len(alt_configs)} alternative configurations:")
            for config in alt_configs:
                print(f"  - {config['name']}: {config['command']}")
        else:
            print("\nNo alternative configurations found.")
    else:
        print("✅ Regular Python installation detected - should work fine!")
